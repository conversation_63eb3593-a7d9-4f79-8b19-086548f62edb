@extends('layouts.dashboard')

@section('title', 'Login History - ' . $user->full_name)

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">Login History</h1>
                    <p class="text-gray-600">
                        Detailed login activity for 
                        <span class="font-medium">{{ $user->full_name }}</span> 
                        ({{ $user->email }})
                    </p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('admin.users.show', $user) }}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to User
                    </a>
                    <a href="{{ route('admin.users.login-history.export', $user) }}?days={{ $days }}" 
                       class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Export CSV
                    </a>
                </div>
            </div>
        </div>

        <!-- Enhanced Filters -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
            <div class="flex flex-wrap items-center gap-4">
                <div>
                    <label for="date-filter" class="block text-sm font-medium text-gray-700 mb-2">Time Period</label>
                    <select id="date-filter" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                        <option value="1" {{ request('days') == 1 ? 'selected' : '' }}>Today</option>
                        <option value="7" {{ request('days') == 7 ? 'selected' : '' }}>Last 7 days</option>
                        <option value="30" {{ request('days') == 30 || (!request('days') && !request('start_date')) ? 'selected' : '' }}>Last 30 days</option>
                        <option value="90" {{ request('days') == 90 ? 'selected' : '' }}>Last 90 days</option>
                        <option value="365" {{ request('days') == 365 ? 'selected' : '' }}>Last year</option>
                        <option value="all" {{ request('days') == 'all' ? 'selected' : '' }}>All time</option>
                        <option value="custom" {{ request('start_date') && request('end_date') ? 'selected' : '' }}>Custom range</option>
                    </select>
                </div>

                <div id="custom-date-range" class="{{ request('start_date') && request('end_date') ? '' : 'hidden' }} flex items-center gap-2">
                    <div>
                        <label for="start-date" class="block text-sm font-medium text-gray-700 mb-2">From</label>
                        <input type="date" id="start-date" value="{{ request('start_date') }}" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    </div>
                    <div>
                        <label for="end-date" class="block text-sm font-medium text-gray-700 mb-2">To</label>
                        <input type="date" id="end-date" value="{{ request('end_date', now()->format('Y-m-d')) }}" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    </div>
                </div>

                <div class="pt-6">
                    <button id="apply-filter" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707v4.586a1 1 0 01-.293.707l-2 2A1 1 0 0110 21v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                        </svg>
                        Apply Filter
                    </button>
                    <button id="reset-filter" class="ml-2 inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        Reset
                    </button>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Logins</p>
                        <p class="text-2xl font-semibold text-gray-900">{{ $stats['total_logins'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 rounded-lg">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Successful</p>
                        <p class="text-2xl font-semibold text-gray-900">{{ $stats['successful_logins'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-red-100 rounded-lg">
                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Failed</p>
                        <p class="text-2xl font-semibold text-gray-900">{{ $stats['failed_logins'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-purple-100 rounded-lg">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Devices</p>
                        <p class="text-2xl font-semibold text-gray-900">{{ $stats['unique_devices'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-orange-100 rounded-lg">
                        <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Locations</p>
                        <p class="text-2xl font-semibold text-gray-900">{{ $stats['unique_locations'] }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Login History Table -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-medium text-gray-900">Detailed Login Activity</h2>
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Time</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP Address</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Device Info</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Session</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Flags</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($loginHistories as $history)
                            <tr class="hover:bg-gray-50" id="login-{{ $history->public_id }}">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ $history->login_at->format('M j, Y') }}</div>
                                    <div class="text-sm text-gray-500">{{ $history->login_at->format('g:i:s A') }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @if($history->login_successful)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Success
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            Failed
                                        </span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900 font-mono">{{ $history->ip_address }}</div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900">{{ $history->device_info }}</div>
                                    <div class="text-sm text-gray-500">{{ $history->user_agent }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ $history->location }}</div>
                                    @if($history->latitude && $history->longitude)
                                        <div class="text-sm text-gray-500">{{ $history->latitude }}, {{ $history->longitude }}</div>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ $history->session_duration_human }}</div>
                                    @if($history->logout_at)
                                        <div class="text-sm text-gray-500">Ended: {{ $history->logout_at->format('g:i A') }}</div>
                                    @else
                                        <div class="text-sm text-gray-500">Active</div>
                                    @endif
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex flex-wrap gap-1">
                                        @if($history->is_new_device)
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                New Device
                                            </span>
                                        @endif
                                        @if($history->is_new_location)
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                New Location
                                            </span>
                                        @endif
                                        @if($history->is_suspicious)
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                Suspicious
                                            </span>
                                        @endif
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        @if(!$history->is_suspicious)
                                            <button onclick="markSuspicious('{{ $history->public_id }}')" 
                                                    class="text-red-600 hover:text-red-900">
                                                Mark Suspicious
                                            </button>
                                        @endif
                                        <button onclick="deleteLogin('{{ $history->public_id }}')" 
                                                class="text-red-600 hover:text-red-900">
                                            Delete
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="8" class="px-6 py-12 text-center">
                                    <div class="text-gray-500">
                                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        <h3 class="mt-2 text-sm font-medium text-gray-900">No login history</h3>
                                        <p class="mt-1 text-sm text-gray-500">No login activity found for the selected time period.</p>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($loginHistories->hasPages())
                <div class="px-6 py-4 border-t border-gray-200">
                    {{ $loginHistories->appends(request()->query())->links() }}
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Mark Suspicious Modal -->
<div id="suspiciousModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <h3 class="text-lg font-medium text-gray-900">Mark as Suspicious</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500">Provide a reason for marking this login as suspicious:</p>
                <textarea id="suspiciousReason" class="mt-3 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500" rows="3" placeholder="Enter reason..."></textarea>
            </div>
            <div class="items-center px-4 py-3">
                <button id="confirmSuspicious" class="px-4 py-2 bg-red-500 text-white text-base font-medium rounded-md w-24 mr-2 hover:bg-red-600">
                    Mark
                </button>
                <button onclick="closeSuspiciousModal()" class="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md w-24 hover:bg-gray-600">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentLoginId = null;

function markSuspicious(loginId) {
    currentLoginId = loginId;
    document.getElementById('suspiciousModal').classList.remove('hidden');
}

function closeSuspiciousModal() {
    document.getElementById('suspiciousModal').classList.add('hidden');
    document.getElementById('suspiciousReason').value = '';
    currentLoginId = null;
}

document.getElementById('confirmSuspicious').addEventListener('click', function() {
    if (!currentLoginId) return;
    
    const reason = document.getElementById('suspiciousReason').value;
    
    fetch(`/admin/users/{{ $user->uuid }}/login-history/${currentLoginId}/mark-suspicious`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ reason: reason })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error marking login as suspicious');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error marking login as suspicious');
    });
    
    closeSuspiciousModal();
});

function deleteLogin(loginId) {
    if (confirm('Are you sure you want to delete this login history entry?')) {
        fetch(`/admin/users/{{ $user->uuid }}/login-history/${loginId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById(`login-${loginId}`).remove();
            } else {
                alert('Error deleting login history');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting login history');
        });
    }
}

// Enhanced date filtering functionality
document.addEventListener('DOMContentLoaded', function() {
    const dateFilter = document.getElementById('date-filter');
    const customDateRange = document.getElementById('custom-date-range');
    const startDate = document.getElementById('start-date');
    const endDate = document.getElementById('end-date');
    const applyFilter = document.getElementById('apply-filter');
    const resetFilter = document.getElementById('reset-filter');

    // Show/hide custom date range
    dateFilter.addEventListener('change', function() {
        if (this.value === 'custom') {
            customDateRange.classList.remove('hidden');
        } else {
            customDateRange.classList.add('hidden');
        }
    });

    // Apply filter
    applyFilter.addEventListener('click', function() {
        const filterValue = dateFilter.value;
        let url = new URL(window.location.href);

        if (filterValue === 'custom') {
            if (!startDate.value || !endDate.value) {
                alert('Please select both start and end dates');
                return;
            }
            url.searchParams.set('start_date', startDate.value);
            url.searchParams.set('end_date', endDate.value);
            url.searchParams.delete('days');
        } else if (filterValue === 'all') {
            url.searchParams.delete('days');
            url.searchParams.delete('start_date');
            url.searchParams.delete('end_date');
        } else {
            url.searchParams.set('days', filterValue);
            url.searchParams.delete('start_date');
            url.searchParams.delete('end_date');
        }

        // Show loading state
        applyFilter.innerHTML = '<svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Loading...';

        window.location.href = url.toString();
    });

    // Reset filter
    resetFilter.addEventListener('click', function() {
        dateFilter.value = '30';
        customDateRange.classList.add('hidden');
        startDate.value = '';
        endDate.value = new Date().toISOString().split('T')[0];

        let url = new URL(window.location.href);
        url.searchParams.delete('days');
        url.searchParams.delete('start_date');
        url.searchParams.delete('end_date');

        window.location.href = url.toString();
    });
});
</script>
@endsection
