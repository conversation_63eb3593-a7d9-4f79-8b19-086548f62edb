@import "tailwindcss";

/* Client Dashboard Specific Styles */

/* Custom Properties for Client Dashboard */
:root {
  /* Primary Colors */
  --color-primary-50: #f8fafc;
  --color-primary-100: #f1f5f9;
  --color-primary-200: #e2e8f0;
  --color-primary-300: #cbd5e1;
  --color-primary-400: #94a3b8;
  --color-primary-500: #64748b;
  --color-primary-600: #1e3a8a;
  --color-primary-700: #1e40af;
  --color-primary-800: #1e293b;
  --color-primary-900: #0f172a;

  /* Secondary Colors */
  --color-secondary-50: #f0fdf4;
  --color-secondary-100: #dcfce7;
  --color-secondary-200: #bbf7d0;
  --color-secondary-300: #86efac;
  --color-secondary-400: #4ade80;
  --color-secondary-500: #22c55e;
  --color-secondary-600: #166534;
  --color-secondary-700: #14532d;
  --color-secondary-800: #052e16;
  --color-secondary-900: #022c22;

  /* Accent Colors */
  --color-accent-50: #eff6ff;
  --color-accent-100: #dbeafe;
  --color-accent-200: #bfdbfe;
  --color-accent-300: #93c5fd;
  --color-accent-400: #60a5fa;
  --color-accent-500: #3b82f6;
  --color-accent-600: #1d4ed8;
  --color-accent-700: #1e40af;
  --color-accent-800: #1e3a8a;
  --color-accent-900: #1e293b;

  /* Neutral Colors */
  --color-neutral-50: #fafafa;
  --color-neutral-100: #f5f5f5;
  --color-neutral-200: #e5e5e5;
  --color-neutral-300: #d4d4d4;
  --color-neutral-400: #a3a3a3;
  --color-neutral-500: #737373;
  --color-neutral-600: #525252;
  --color-neutral-700: #404040;
  --color-neutral-800: #262626;
  --color-neutral-900: #171717;
}

/* Background colors using CSS variables */
.bg-primary-600 {
    background-color: var(--color-primary-600, #1e3a8a) !important;
}

.bg-primary-700 {
    background-color: var(--color-primary-700, #1e40af) !important;
}

.bg-primary-800 {
    background-color: var(--color-primary-800, #1e293b) !important;
}

.bg-secondary-600 {
    background-color: var(--color-secondary-600, #166534) !important;
}

.bg-secondary-700 {
    background-color: var(--color-secondary-700, #14532d) !important;
}

.bg-accent-600 {
    background-color: var(--color-accent-600, #1d4ed8) !important;
}

.bg-neutral-50 {
    background-color: var(--color-neutral-50, #fafafa) !important;
}

.bg-neutral-100 {
    background-color: var(--color-neutral-100, #f5f5f5) !important;
}

/* Text colors using CSS variables */
.text-primary-600 {
    color: var(--color-primary-600, #1e3a8a) !important;
}

.text-primary-700 {
    color: var(--color-primary-700, #1e40af) !important;
}

.text-primary-800 {
    color: var(--color-primary-800, #1e293b) !important;
}

.text-primary-900 {
    color: var(--color-primary-900, #0f172a) !important;
}

.text-secondary-600 {
    color: var(--color-secondary-600, #166534) !important;
}

.text-secondary-700 {
    color: var(--color-secondary-700, #14532d) !important;
}

/* Gradient classes for dashboard welcome section */
.from-primary-800 {
    --tw-gradient-from: var(--color-primary-800, #1e293b) !important;
    --tw-gradient-to: rgba(30, 41, 59, 0) !important;
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
}

.via-primary-700 {
    --tw-gradient-to: rgba(30, 64, 175, 0) !important;
    --tw-gradient-stops: var(--tw-gradient-from), var(--color-primary-700, #1e40af), var(--tw-gradient-to) !important;
}

.to-secondary-700 {
    --tw-gradient-to: var(--color-secondary-700, #14532d) !important;
}

.bg-gradient-to-br {
    background-image: linear-gradient(to bottom right, var(--tw-gradient-stops)) !important;
}

/* Ensure text visibility on gradient backgrounds */
.bg-gradient-to-br.text-white,
.bg-gradient-to-br .text-white {
    color: #ffffff !important;
}

.bg-gradient-to-br .text-blue-100 {
    color: #dbeafe !important;
}

.bg-gradient-to-br .text-blue-200 {
    color: #bfdbfe !important;
}

/* Shadow classes for dashboard components */
.shadow-strong {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
}

.shadow-soft {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
}

.shadow-medium {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

/* Client Dashboard Components */
.dashboard-card {
    @apply bg-white rounded-xl p-6 border border-neutral-100;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.dashboard-stat {
    @apply flex items-center justify-between;
}

.dashboard-stat-content h3 {
    @apply text-2xl font-bold text-gray-900;
}

.dashboard-stat-content p {
    @apply text-sm text-gray-600;
}

.dashboard-stat-icon {
    @apply w-12 h-12 rounded-lg flex items-center justify-center;
}

/* Button Styles for Client Dashboard */
.btn-primary {
    @apply bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors;
}

.btn-secondary {
    @apply bg-secondary-600 text-white px-4 py-2 rounded-lg hover:bg-secondary-700 transition-colors;
}

.btn-outline {
    @apply border border-primary-600 text-primary-600 px-4 py-2 rounded-lg hover:bg-primary-600 hover:text-white transition-colors;
}

/* Card Styles */
.card {
    @apply bg-white rounded-xl shadow-soft border border-neutral-100;
}

.card-header {
    @apply px-6 py-4 border-b border-neutral-200;
}

.card-body {
    @apply p-6;
}

.card-footer {
    @apply px-6 py-4 border-t border-neutral-200 bg-neutral-50;
}

/* Table Styles */
.table-container {
    @apply bg-white rounded-xl shadow-soft overflow-hidden;
}

.table {
    @apply w-full divide-y divide-neutral-200;
}

.table th {
    @apply px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider bg-neutral-50;
}

.table td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-neutral-900;
}

/* Form Styles */
.form-group {
    @apply mb-4;
}

.form-label {
    @apply block text-sm font-medium text-neutral-700 mb-2;
}

.form-input {
    @apply w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
}

.form-select {
    @apply w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
}

.form-textarea {
    @apply w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 resize-vertical;
}

/* Alert Styles */
.alert {
    @apply p-4 rounded-lg mb-4;
}

.alert-success {
    @apply bg-green-50 border border-green-200 text-green-800;
}

.alert-error {
    @apply bg-red-50 border border-red-200 text-red-800;
}

.alert-warning {
    @apply bg-yellow-50 border border-yellow-200 text-yellow-800;
}

.alert-info {
    @apply bg-blue-50 border border-blue-200 text-blue-800;
}

/* Responsive Design */
@media (max-width: 640px) {
    .dashboard-card {
        @apply p-4;
    }
    
    .card-body {
        @apply p-4;
    }
    
    .table-container {
        @apply rounded-lg;
    }
}
