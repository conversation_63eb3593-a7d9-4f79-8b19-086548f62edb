<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class LoginHistory extends Model
{
    use HasFactory;
    protected $fillable = [
        'public_id',
        'user_id',
        'login_at',
        'logout_at',
        'login_successful',
        'login_method',
        'failure_reason',
        'ip_address',
        'user_agent',
        'device_fingerprint',
        'device_type',
        'device_name',
        'browser_name',
        'browser_version',
        'operating_system',
        'platform',
        'country',
        'country_code',
        'region',
        'city',
        'timezone',
        'latitude',
        'longitude',
        'session_id',
        'session_duration',
        'is_new_device',
        'is_new_location',
        'is_suspicious',
        'security_notes',
        'additional_data',
        'is_deleted',
    ];

    protected $casts = [
        'login_at' => 'datetime',
        'logout_at' => 'datetime',
        'login_successful' => 'boolean',
        'is_new_device' => 'boolean',
        'is_new_location' => 'boolean',
        'is_suspicious' => 'boolean',
        'is_deleted' => 'boolean',
        'additional_data' => 'array',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->public_id)) {
                $model->public_id = Str::uuid();
            }
        });
    }

    /**
     * Get the user that owns the login history.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to get only successful logins.
     */
    public function scopeSuccessful($query)
    {
        return $query->where('login_successful', true);
    }

    /**
     * Scope to get only failed logins.
     */
    public function scopeFailed($query)
    {
        return $query->where('login_successful', false);
    }

    /**
     * Scope to get suspicious logins.
     */
    public function scopeSuspicious($query)
    {
        return $query->where('is_suspicious', true);
    }

    /**
     * Scope to get logins from new devices.
     */
    public function scopeNewDevices($query)
    {
        return $query->where('is_new_device', true);
    }

    /**
     * Scope to get logins from new locations.
     */
    public function scopeNewLocations($query)
    {
        return $query->where('is_new_location', true);
    }

    /**
     * Scope to get active (non-deleted) records.
     */
    public function scopeActive($query)
    {
        return $query->where('is_deleted', false);
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'public_id';
    }

    /**
     * Get formatted location string.
     */
    public function getLocationAttribute(): string
    {
        $parts = array_filter([$this->city, $this->region, $this->country]);
        return implode(', ', $parts) ?: 'Unknown Location';
    }

    /**
     * Get formatted device string.
     */
    public function getDeviceInfoAttribute(): string
    {
        $parts = array_filter([
            $this->device_name,
            $this->browser_name . ($this->browser_version ? ' ' . $this->browser_version : ''),
            $this->operating_system
        ]);
        return implode(' - ', $parts) ?: 'Unknown Device';
    }

    /**
     * Get session duration in human readable format.
     */
    public function getSessionDurationHumanAttribute(): string
    {
        if (!$this->session_duration) {
            return 'Unknown';
        }

        $hours = floor($this->session_duration / 3600);
        $minutes = floor(($this->session_duration % 3600) / 60);
        $seconds = $this->session_duration % 60;

        if ($hours > 0) {
            return sprintf('%dh %dm %ds', $hours, $minutes, $seconds);
        } elseif ($minutes > 0) {
            return sprintf('%dm %ds', $minutes, $seconds);
        } else {
            return sprintf('%ds', $seconds);
        }
    }

    /**
     * Check if this login is from a trusted device/location combination.
     */
    public function isTrusted(): bool
    {
        return !$this->is_new_device && !$this->is_new_location && !$this->is_suspicious;
    }

    /**
     * Mark this login as suspicious.
     */
    public function markAsSuspicious(string $reason = null): void
    {
        $this->update([
            'is_suspicious' => true,
            'security_notes' => $reason
        ]);
    }
}
