<?php

namespace App\Listeners;

use App\Services\LoginHistoryService;
use Illuminate\Auth\Events\Login;
use Illuminate\Auth\Events\Logout;
use Illuminate\Auth\Events\Failed;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class LoginH<PERSON>oryListener implements ShouldQueue
{
    use InteractsWithQueue;

    protected LoginHistoryService $loginHistoryService;

    /**
     * Create the event listener.
     */
    public function __construct(LoginHistoryService $loginHistoryService)
    {
        $this->loginHistoryService = $loginHistoryService;
    }

    /**
     * Handle successful login events.
     */
    public function handleLogin(Login $event): void
    {
        $request = request();

        $this->loginHistoryService->recordLogin(
            $event->user,
            $request,
            true,
            'email' // Default method, can be enhanced to detect actual method
        );
    }

    /**
     * Handle logout events.
     */
    public function handleLogout(Logout $event): void
    {
        if ($event->user) {
            $request = request();
            $this->loginHistoryService->recordLogout($event->user, $request);
        }
    }

    /**
     * <PERSON>le failed login events.
     */
    public function handleFailed(Failed $event): void
    {
        if ($event->user) {
            $request = request();

            $this->loginHistoryService->recordLogin(
                $event->user,
                $request,
                false,
                'email',
                'Invalid credentials'
            );
        }
    }

    /**
     * Handle the event (generic handler for backwards compatibility).
     */
    public function handle(object $event): void
    {
        if ($event instanceof Login) {
            $this->handleLogin($event);
        } elseif ($event instanceof Logout) {
            $this->handleLogout($event);
        } elseif ($event instanceof Failed) {
            $this->handleFailed($event);
        }
    }
}
