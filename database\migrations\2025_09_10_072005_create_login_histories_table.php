<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('login_histories', function (Blueprint $table) {
            $table->id();
            $table->uuid('public_id')->unique();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');

            // Login Details
            $table->timestamp('login_at');
            $table->timestamp('logout_at')->nullable();
            $table->boolean('login_successful')->default(true);
            $table->string('login_method')->default('email'); // email, social, api, etc.
            $table->text('failure_reason')->nullable(); // if login failed

            // Network Information
            $table->string('ip_address', 45); // IPv6 support
            $table->string('user_agent', 1000)->nullable();
            $table->string('device_fingerprint', 255)->nullable();

            // Device Information
            $table->string('device_type', 50)->nullable(); // desktop, mobile, tablet
            $table->string('device_name', 100)->nullable(); // iPhone, Samsung Galaxy, etc.
            $table->string('browser_name', 50)->nullable();
            $table->string('browser_version', 20)->nullable();
            $table->string('operating_system', 50)->nullable();
            $table->string('platform', 50)->nullable();

            // Location Information
            $table->string('country', 100)->nullable();
            $table->string('country_code', 2)->nullable();
            $table->string('region', 100)->nullable();
            $table->string('city', 100)->nullable();
            $table->string('timezone', 50)->nullable();
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();

            // Session Information
            $table->string('session_id', 255)->nullable();
            $table->integer('session_duration')->nullable(); // in seconds
            $table->boolean('is_new_device')->default(false);
            $table->boolean('is_new_location')->default(false);

            // Security Information
            $table->boolean('is_suspicious')->default(false);
            $table->text('security_notes')->nullable();
            $table->json('additional_data')->nullable(); // for future extensibility

            // Soft deletes and timestamps
            $table->boolean('is_deleted')->default(false);
            $table->timestamps();

            // Indexes for performance
            $table->index(['user_id', 'login_at']);
            $table->index(['ip_address', 'login_at']);
            $table->index(['device_fingerprint', 'login_at']);
            $table->index(['is_suspicious', 'login_at']);
            $table->index(['login_successful', 'login_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('login_histories');
    }
};
