<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Service;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use App\Services\ActivityLogger;

class ServiceController extends Controller
{
    protected ActivityLogger $activityLogger;

    public function __construct(ActivityLogger $activityLogger)
    {
        $this->activityLogger = $activityLogger;
    }

    /**
     * Display a listing of services.
     */
    public function index(Request $request): View
    {
        $query = Service::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('short_description', 'like', "%{$search}%");
            });
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('is_active', $request->get('status') === 'active');
        }

        // Featured filter
        if ($request->filled('featured')) {
            $query->where('is_featured', $request->get('featured') === 'yes');
        }

        $services = $query->orderBy('sort_order', 'asc')
                         ->orderBy('created_at', 'desc')
                         ->paginate(20);

        // Log activity
        $this->activityLogger->log(
            'admin_services_viewed',
            'Admin viewed services list',
            ['total_services' => $services->total()]
        );

        return view('admin.services.index', compact('services'));
    }

    /**
     * Show the form for creating a new service.
     */
    public function create(): View
    {
        return view('admin.services.create');
    }

    /**
     * Store a newly created service.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:services,slug',
            'short_description' => 'required|string|max:500',
            'description' => 'required|string',
            'price' => 'nullable|numeric|min:0',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
        ]);

        $service = Service::create($validated);

        // Log activity
        $this->activityLogger->log(
            'admin_service_created',
            'Admin created new service',
            ['service_id' => $service->id, 'service_name' => $service->name]
        );

        return redirect()->route('admin.services.index')
                        ->with('success', 'Service created successfully.');
    }

    /**
     * Display the specified service.
     */
    public function show(Service $service): View
    {
        // Log activity
        $this->activityLogger->log(
            'admin_service_viewed',
            'Admin viewed service details',
            ['service_id' => $service->id, 'service_name' => $service->name]
        );

        return view('admin.services.show', compact('service'));
    }

    /**
     * Show the form for editing the specified service.
     */
    public function edit(Service $service): View
    {
        return view('admin.services.edit', compact('service'));
    }

    /**
     * Update the specified service.
     */
    public function update(Request $request, Service $service): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:services,slug,' . $service->id,
            'short_description' => 'required|string|max:500',
            'description' => 'required|string',
            'price' => 'nullable|numeric|min:0',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
        ]);

        $service->update($validated);

        // Log activity
        $this->activityLogger->log(
            'admin_service_updated',
            'Admin updated service',
            ['service_id' => $service->id, 'service_name' => $service->name]
        );

        return redirect()->route('admin.services.index')
                        ->with('success', 'Service updated successfully.');
    }

    /**
     * Remove the specified service.
     */
    public function destroy(Service $service): RedirectResponse
    {
        $serviceName = $service->name;
        $service->delete();

        // Log activity
        $this->activityLogger->log(
            'admin_service_deleted',
            'Admin deleted service',
            ['service_name' => $serviceName]
        );

        return redirect()->route('admin.services.index')
                        ->with('success', 'Service deleted successfully.');
    }

    /**
     * Toggle service featured status.
     */
    public function toggleFeatured(Service $service): JsonResponse
    {
        $service->update(['is_featured' => !$service->is_featured]);

        // Log activity
        $this->activityLogger->log(
            'admin_service_featured_toggled',
            'Admin toggled service featured status',
            [
                'service_id' => $service->id,
                'service_name' => $service->name,
                'is_featured' => $service->is_featured
            ]
        );

        return response()->json([
            'success' => true,
            'is_featured' => $service->is_featured,
            'message' => $service->is_featured ? 'Service marked as featured' : 'Service removed from featured'
        ]);
    }

    /**
     * Toggle service active status.
     */
    public function toggleStatus(Service $service): JsonResponse
    {
        $service->update(['is_active' => !$service->is_active]);

        // Log activity
        $this->activityLogger->log(
            'admin_service_status_toggled',
            'Admin toggled service status',
            [
                'service_id' => $service->id,
                'service_name' => $service->name,
                'is_active' => $service->is_active
            ]
        );

        return response()->json([
            'success' => true,
            'is_active' => $service->is_active,
            'message' => $service->is_active ? 'Service activated' : 'Service deactivated'
        ]);
    }
}
