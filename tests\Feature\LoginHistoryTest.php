<?php

namespace Tests\Feature;

use App\Models\LoginHistory;
use App\Models\User;
use App\Services\LoginHistoryService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Hash;
use Illuminate\Auth\Events\Login;
use Illuminate\Auth\Events\Logout;
use Illuminate\Auth\Events\Failed;
use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;

class LoginHistoryTest extends TestCase
{
    use RefreshDatabase;

    protected $seed = true;

    #[Test]
    public function login_history_is_recorded_on_successful_login()
    {
        Event::fake();

        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
        ]);

        $response = $this->postJson('/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        $response->assertStatus(200);

        // Verify login event was fired
        Event::assertDispatched(Login::class, function ($event) use ($user) {
            return $event->user->id === $user->id;
        });
    }

    #[Test]
    public function login_history_service_records_successful_login()
    {
        $user = User::factory()->create();
        $service = app(LoginHistoryService::class);
        
        $request = $this->createMockRequest();
        
        $loginHistory = $service->recordLogin($user, $request, true, 'email');
        
        $this->assertInstanceOf(LoginHistory::class, $loginHistory);
        $this->assertTrue($loginHistory->login_successful);
        $this->assertEquals($user->id, $loginHistory->user_id);
        $this->assertEquals('email', $loginHistory->login_method);
        $this->assertNotNull($loginHistory->ip_address);
        $this->assertNotNull($loginHistory->user_agent);
        $this->assertNotNull($loginHistory->device_fingerprint);
    }

    #[Test]
    public function login_history_service_records_failed_login()
    {
        $user = User::factory()->create();
        $service = app(LoginHistoryService::class);
        
        $request = $this->createMockRequest();
        
        $loginHistory = $service->recordLogin($user, $request, false, 'email', 'Invalid credentials');
        
        $this->assertInstanceOf(LoginHistory::class, $loginHistory);
        $this->assertFalse($loginHistory->login_successful);
        $this->assertEquals('Invalid credentials', $loginHistory->failure_reason);
        $this->assertEquals($user->id, $loginHistory->user_id);
    }

    #[Test]
    public function login_history_service_records_logout()
    {
        $user = User::factory()->create();
        $service = app(LoginHistoryService::class);
        
        $request = $this->createMockRequest();
        
        // First record a login
        $loginHistory = $service->recordLogin($user, $request, true, 'email');
        
        // Then record logout
        $service->recordLogout($user, $request);
        
        $loginHistory->refresh();
        $this->assertNotNull($loginHistory->logout_at);
        $this->assertNotNull($loginHistory->session_duration);
    }

    #[Test]
    public function login_history_detects_new_device()
    {
        $user = User::factory()->create();
        $service = app(LoginHistoryService::class);
        
        $request = $this->createMockRequest();
        
        $loginHistory = $service->recordLogin($user, $request, true, 'email');
        
        // First login should be marked as new device
        $this->assertTrue($loginHistory->is_new_device);
        
        // Second login with same device should not be new
        $loginHistory2 = $service->recordLogin($user, $request, true, 'email');
        $this->assertFalse($loginHistory2->is_new_device);
    }

    #[Test]
    public function login_history_detects_new_location()
    {
        $user = User::factory()->create();
        $service = app(LoginHistoryService::class);
        
        $request1 = $this->createMockRequest('***********');
        $request2 = $this->createMockRequest('********');
        
        $loginHistory1 = $service->recordLogin($user, $request1, true, 'email');
        $this->assertTrue($loginHistory1->is_new_location);
        
        $loginHistory2 = $service->recordLogin($user, $request2, true, 'email');
        $this->assertTrue($loginHistory2->is_new_location);
        
        // Same IP should not be new location
        $loginHistory3 = $service->recordLogin($user, $request1, true, 'email');
        $this->assertFalse($loginHistory3->is_new_location);
    }

    #[Test]
    public function login_history_calculates_statistics()
    {
        $user = User::factory()->create();
        $service = app(LoginHistoryService::class);
        
        $request = $this->createMockRequest();
        
        // Create some login history
        $service->recordLogin($user, $request, true, 'email');
        $service->recordLogin($user, $request, true, 'email');
        $service->recordLogin($user, $request, false, 'email', 'Invalid password');
        
        $stats = $service->getLoginStats($user, 30);
        
        $this->assertEquals(3, $stats['total_logins']);
        $this->assertEquals(2, $stats['successful_logins']);
        $this->assertEquals(1, $stats['failed_logins']);
        $this->assertEquals(66.67, $stats['success_rate']);
        $this->assertGreaterThanOrEqual(1, $stats['unique_devices']);
        $this->assertGreaterThanOrEqual(1, $stats['unique_locations']);
    }

    #[Test]
    public function user_can_view_own_login_history()
    {
        $user = User::factory()->create();

        // Create some login history
        LoginHistory::factory()->create([
            'user_id' => $user->id,
            'login_successful' => true,
        ]);

        $response = $this->actingAs($user)->get('/login-history');

        $response->assertStatus(200);
        $response->assertSee('Login History');
        $response->assertSee('Recent Login Activity');
    }

    #[Test]
    public function admin_can_view_user_login_history()
    {
        $admin = User::factory()->create();
        $admin->role->update(['name' => 'admin']);
        
        $user = User::factory()->create();
        
        // Create some login history
        LoginHistory::factory()->create([
            'user_id' => $user->id,
            'login_successful' => true,
        ]);
        
        $response = $this->actingAs($admin)->get("/admin/users/{$user->uuid}/login-history");

        $response->assertStatus(200);
        $response->assertSee('Login History');
        $response->assertSee($user->first_name);
    }

    #[Test]
    public function non_admin_cannot_view_other_users_login_history()
    {
        $user1 = User::factory()->create(['role_id' => 3]); // Regular user
        $user2 = User::factory()->create(['role_id' => 3]); // Regular user

        $response = $this->actingAs($user1)->get("/admin/users/{$user2->uuid}/login-history");

        $response->assertStatus(403);
    }

    #[Test]
    public function admin_can_mark_login_as_suspicious()
    {
        $admin = User::factory()->create();
        $admin->role->update(['name' => 'admin']);
        
        $user = User::factory()->create();
        $loginHistory = LoginHistory::factory()->create([
            'user_id' => $user->id,
            'is_suspicious' => false,
        ]);
        
        $response = $this->actingAs($admin)
            ->postJson("/admin/users/{$user->uuid}/login-history/{$loginHistory->public_id}/mark-suspicious", [
                'reason' => 'Unusual login pattern'
            ]);
        
        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
        
        $loginHistory->refresh();
        $this->assertTrue($loginHistory->is_suspicious);
        $this->assertEquals('Unusual login pattern', $loginHistory->security_notes);
    }

    #[Test]
    public function admin_can_export_login_history()
    {
        $admin = User::factory()->create();
        $admin->role->update(['name' => 'admin']);
        
        $user = User::factory()->create();
        LoginHistory::factory()->create([
            'user_id' => $user->id,
            'login_successful' => true,
        ]);
        
        $response = $this->actingAs($admin)->get("/admin/users/{$user->uuid}/login-history/export");
        
        $response->assertStatus(200);
        $response->assertHeader('content-type', 'text/csv; charset=UTF-8');
    }

    #[Test]
    public function login_history_can_be_soft_deleted()
    {
        $user = User::factory()->create();
        $loginHistory = LoginHistory::factory()->create([
            'user_id' => $user->id,
        ]);
        
        $response = $this->actingAs($user)
            ->deleteJson("/login-history/{$loginHistory->public_id}");
        
        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
        
        $loginHistory->refresh();
        $this->assertTrue($loginHistory->is_deleted);
    }

    /**
     * Create a mock request for testing.
     */
    protected function createMockRequest(string $ip = '***********'): \Illuminate\Http\Request
    {
        $request = new \Illuminate\Http\Request();
        $request->server->set('REMOTE_ADDR', $ip);
        $request->server->set('HTTP_USER_AGENT', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        $request->server->set('HTTP_ACCEPT_LANGUAGE', 'en-US,en;q=0.9');
        $request->server->set('HTTP_ACCEPT_ENCODING', 'gzip, deflate');
        
        // Mock session
        $session = new \Illuminate\Session\Store('test', new \Illuminate\Session\ArraySessionHandler(60));
        $session->setId('test-session-id');
        $request->setLaravelSession($session);
        
        return $request;
    }

    #[Test]
    public function admin_login_creates_login_history_record()
    {
        // Create admin user with specific credentials
        $admin = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role_id' => 1, // Admin role
        ]);

        // Ensure no login history exists initially
        $this->assertDatabaseMissing('login_histories', [
            'user_id' => $admin->id,
        ]);

        // Attempt admin login
        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
        ]);

        // Assert login was successful and redirected to admin dashboard
        $response->assertRedirect('/admin');
        $this->assertAuthenticatedAs($admin);

        // Assert login history was recorded
        $this->assertDatabaseHas('login_histories', [
            'user_id' => $admin->id,
            'login_successful' => true,
        ]);

        // Get the login history record and verify details
        $loginHistory = LoginHistory::where('user_id', $admin->id)->first();
        $this->assertNotNull($loginHistory, 'Login history record should be created');
        $this->assertTrue($loginHistory->login_successful, 'Login should be marked as successful');
        $this->assertNotNull($loginHistory->ip_address, 'IP address should be recorded');
        $this->assertNotNull($loginHistory->user_agent, 'User agent should be recorded');
        $this->assertNotNull($loginHistory->login_at, 'Login timestamp should be recorded');
        $this->assertNull($loginHistory->logout_at, 'Logout timestamp should be null for active session');
    }
}
