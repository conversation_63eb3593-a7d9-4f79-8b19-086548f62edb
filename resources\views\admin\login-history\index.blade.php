@extends('layouts.admin')

@section('title', 'Global Login History Dashboard')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Global Login History Dashboard</h1>
            <p class="text-gray-600 mt-2">Monitor and analyze login activity across all users</p>
        </div>
        <div class="flex space-x-3">
            <button id="export-btn" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Export CSV
            </button>
            <button id="refresh-btn" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Refresh
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Logins</dt>
                        <dd class="text-lg font-medium text-gray-900" id="total-logins">{{ number_format($stats['total_logins']) }}</dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Success Rate</dt>
                        <dd class="text-lg font-medium text-gray-900" id="success-rate">{{ $stats['success_rate'] }}%</dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Suspicious Logins</dt>
                        <dd class="text-lg font-medium text-gray-900" id="suspicious-logins">{{ number_format($stats['suspicious_logins']) }}</dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Unique Users</dt>
                        <dd class="text-lg font-medium text-gray-900" id="unique-users">{{ number_format($stats['unique_users']) }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Advanced Filters -->
    <div class="bg-white rounded-lg shadow p-6 mb-8">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Advanced Filters</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- Search -->
            <div>
                <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                <input type="text" id="search" value="{{ $search }}" placeholder="Email, name, IP, device..." class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
            </div>

            <!-- User Filter -->
            <div>
                <label for="user-filter" class="block text-sm font-medium text-gray-700 mb-2">User</label>
                <select id="user-filter" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                    <option value="">All Users</option>
                    @foreach($users as $user)
                        <option value="{{ $user->id }}" {{ $userFilter == $user->id ? 'selected' : '' }}>
                            {{ $user->full_name }} ({{ $user->email }})
                        </option>
                    @endforeach
                </select>
            </div>

            <!-- Status Filter -->
            <div>
                <label for="status-filter" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select id="status-filter" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                    <option value="">All Status</option>
                    <option value="successful" {{ $statusFilter == 'successful' ? 'selected' : '' }}>Successful</option>
                    <option value="failed" {{ $statusFilter == 'failed' ? 'selected' : '' }}>Failed</option>
                    <option value="suspicious" {{ $statusFilter == 'suspicious' ? 'selected' : '' }}>Suspicious</option>
                    <option value="new_device" {{ $statusFilter == 'new_device' ? 'selected' : '' }}>New Device</option>
                    <option value="new_location" {{ $statusFilter == 'new_location' ? 'selected' : '' }}>New Location</option>
                </select>
            </div>

            <!-- Date Filter -->
            <div>
                <label for="date-filter" class="block text-sm font-medium text-gray-700 mb-2">Time Period</label>
                <select id="date-filter" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                    <option value="1" {{ request('days') == 1 ? 'selected' : '' }}>Today</option>
                    <option value="7" {{ request('days') == 7 ? 'selected' : '' }}>Last 7 days</option>
                    <option value="30" {{ request('days') == 30 || (!request('days') && !request('start_date')) ? 'selected' : '' }}>Last 30 days</option>
                    <option value="90" {{ request('days') == 90 ? 'selected' : '' }}>Last 90 days</option>
                    <option value="365" {{ request('days') == 365 ? 'selected' : '' }}>Last year</option>
                    <option value="all" {{ request('days') == 'all' ? 'selected' : '' }}>All time</option>
                    <option value="custom" {{ request('start_date') && request('end_date') ? 'selected' : '' }}>Custom range</option>
                </select>
            </div>
        </div>

        <!-- Custom Date Range -->
        <div id="custom-date-range" class="{{ request('start_date') && request('end_date') ? '' : 'hidden' }} mt-4 flex items-center gap-4">
            <div>
                <label for="start-date" class="block text-sm font-medium text-gray-700 mb-2">From</label>
                <input type="date" id="start-date" value="{{ request('start_date') }}" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
            </div>
            <div>
                <label for="end-date" class="block text-sm font-medium text-gray-700 mb-2">To</label>
                <input type="date" id="end-date" value="{{ request('end_date', now()->format('Y-m-d')) }}" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
            </div>
        </div>

        <!-- Filter Actions -->
        <div class="mt-4 flex space-x-3">
            <button id="apply-filters" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707v4.586a1 1 0 01-.293.707l-2 2A1 1 0 0110 21v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                </svg>
                Apply Filters
            </button>
            <button id="clear-filters" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                Clear All
            </button>
        </div>
    </div>

    <!-- Login History Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Login History</h3>
            <p class="text-sm text-gray-500 mt-1">Showing {{ $loginHistories->firstItem() ?? 0 }} to {{ $loginHistories->lastItem() ?? 0 }} of {{ $loginHistories->total() }} results</p>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Login Time</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Device</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP Address</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Flags</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200" id="login-history-tbody">
                    @forelse($loginHistories as $history)
                        <tr id="login-{{ $history->public_id }}" class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                            <span class="text-sm font-medium text-gray-700">
                                                {{ substr($history->user->first_name ?? 'U', 0, 1) }}{{ substr($history->user->last_name ?? 'U', 0, 1) }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">{{ $history->user->full_name ?? 'Unknown User' }}</div>
                                        <div class="text-sm text-gray-500">{{ $history->user->email ?? 'Unknown' }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ $history->login_at->format('M j, Y') }}</div>
                                <div class="text-sm text-gray-500">{{ $history->login_at->format('g:i A') }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($history->login_successful)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        Success
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                        </svg>
                                        Failed
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ $history->city }}, {{ $history->country }}</div>
                                <div class="text-sm text-gray-500">{{ $history->country_code }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ $history->device_type }}</div>
                                <div class="text-sm text-gray-500">{{ $history->browser_name }} {{ $history->browser_version }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ $history->ip_address }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex flex-wrap gap-1">
                                    @if($history->is_suspicious)
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            Suspicious
                                        </span>
                                    @endif
                                    @if($history->is_new_device)
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            New Device
                                        </span>
                                    @endif
                                    @if($history->is_new_location)
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            New Location
                                        </span>
                                    @endif
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex space-x-2">
                                    <a href="/admin/users/{{ $history->user->uuid }}/login-history" class="text-blue-600 hover:text-blue-900">View User</a>
                                    @if(!$history->is_suspicious)
                                        <button onclick="markSuspicious('{{ $history->public_id }}')" class="text-yellow-600 hover:text-yellow-900">Mark Suspicious</button>
                                    @endif
                                    <button onclick="deleteLogin('{{ $history->public_id }}')" class="text-red-600 hover:text-red-900">Delete</button>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="8" class="px-6 py-4 text-center text-gray-500">
                                No login history found matching your criteria.
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        @if($loginHistories->hasPages())
            <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                {{ $loginHistories->appends(request()->query())->links() }}
            </div>
        @endif
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search');
    const userFilter = document.getElementById('user-filter');
    const statusFilter = document.getElementById('status-filter');
    const dateFilter = document.getElementById('date-filter');
    const customDateRange = document.getElementById('custom-date-range');
    const startDate = document.getElementById('start-date');
    const endDate = document.getElementById('end-date');
    const applyFilters = document.getElementById('apply-filters');
    const clearFilters = document.getElementById('clear-filters');
    const exportBtn = document.getElementById('export-btn');
    const refreshBtn = document.getElementById('refresh-btn');

    // Show/hide custom date range
    dateFilter.addEventListener('change', function() {
        if (this.value === 'custom') {
            customDateRange.classList.remove('hidden');
        } else {
            customDateRange.classList.add('hidden');
        }
    });

    // Apply filters
    applyFilters.addEventListener('click', function() {
        applyCurrentFilters();
    });

    // Clear all filters
    clearFilters.addEventListener('click', function() {
        searchInput.value = '';
        userFilter.value = '';
        statusFilter.value = '';
        dateFilter.value = '30';
        customDateRange.classList.add('hidden');
        startDate.value = '';
        endDate.value = new Date().toISOString().split('T')[0];

        // Redirect to clean URL
        window.location.href = window.location.pathname;
    });

    // Export functionality
    exportBtn.addEventListener('click', function() {
        const url = new URL('/admin/login-history/export', window.location.origin);
        addCurrentFiltersToUrl(url);

        // Show loading state
        exportBtn.innerHTML = '<svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Exporting...';

        // Create download link
        const link = document.createElement('a');
        link.href = url.toString();
        link.download = '';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Reset button after delay
        setTimeout(() => {
            exportBtn.innerHTML = '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg>Export CSV';
        }, 2000);
    });

    // Refresh functionality
    refreshBtn.addEventListener('click', function() {
        refreshBtn.innerHTML = '<svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-gray-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Refreshing...';

        // Refresh with current filters
        applyCurrentFilters();
    });

    // Search on Enter key
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            applyCurrentFilters();
        }
    });

    // Auto-apply filters on change (with debounce for search)
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            applyCurrentFilters();
        }, 500);
    });

    userFilter.addEventListener('change', applyCurrentFilters);
    statusFilter.addEventListener('change', applyCurrentFilters);

    function applyCurrentFilters() {
        const url = new URL(window.location.href);
        addCurrentFiltersToUrl(url);

        // Show loading state
        applyFilters.innerHTML = '<svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Loading...';

        window.location.href = url.toString();
    }

    function addCurrentFiltersToUrl(url) {
        // Clear existing params
        url.searchParams.delete('search');
        url.searchParams.delete('user_id');
        url.searchParams.delete('status');
        url.searchParams.delete('days');
        url.searchParams.delete('start_date');
        url.searchParams.delete('end_date');

        // Add current filter values
        if (searchInput.value.trim()) {
            url.searchParams.set('search', searchInput.value.trim());
        }

        if (userFilter.value) {
            url.searchParams.set('user_id', userFilter.value);
        }

        if (statusFilter.value) {
            url.searchParams.set('status', statusFilter.value);
        }

        if (dateFilter.value === 'custom') {
            if (startDate.value && endDate.value) {
                url.searchParams.set('start_date', startDate.value);
                url.searchParams.set('end_date', endDate.value);
            }
        } else if (dateFilter.value !== 'all') {
            url.searchParams.set('days', dateFilter.value);
        }
    }
});

// Mark login as suspicious
function markSuspicious(loginId) {
    const reason = prompt('Please provide a reason for marking this login as suspicious:');
    if (reason === null) return; // User cancelled

    fetch(`/admin/login-history/${loginId}/mark-suspicious`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ reason: reason })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Refresh the page to show updated status
            window.location.reload();
        } else {
            alert('Error marking login as suspicious');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error marking login as suspicious');
    });
}

// Delete login history entry
function deleteLogin(loginId) {
    if (confirm('Are you sure you want to delete this login history entry? This action cannot be undone.')) {
        fetch(`/admin/login-history/${loginId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById(`login-${loginId}`).remove();
            } else {
                alert('Error deleting login history');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting login history');
        });
    }
}
</script>
@endsection
