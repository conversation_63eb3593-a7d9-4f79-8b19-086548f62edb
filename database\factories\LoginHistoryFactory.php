<?php

namespace Database\Factories;

use App\Models\LoginHistory;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\LoginHistory>
 */
class LoginHistoryFactory extends Factory
{
    protected $model = LoginHistory::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $loginAt = $this->faker->dateTimeBetween('-30 days', 'now');
        $logoutAt = $this->faker->boolean(70) ? $this->faker->dateTimeBetween($loginAt, 'now') : null;
        $sessionDuration = $logoutAt ? $logoutAt->getTimestamp() - $loginAt->getTimestamp() : null;

        return [
            'public_id' => Str::uuid(),
            'user_id' => User::factory(),
            'login_at' => $loginAt,
            'logout_at' => $logoutAt,
            'login_successful' => $this->faker->boolean(90),
            'login_method' => $this->faker->randomElement(['email', 'social', 'api']),
            'failure_reason' => $this->faker->boolean(10) ? $this->faker->randomElement([
                'Invalid credentials',
                'Account locked',
                'Rate limited',
                'Account disabled'
            ]) : null,
            'ip_address' => $this->faker->ipv4(),
            'user_agent' => $this->faker->userAgent(),
            'device_fingerprint' => hash('sha256', $this->faker->uuid()),
            'device_type' => $this->faker->randomElement(['desktop', 'mobile', 'tablet']),
            'device_name' => $this->faker->randomElement(['iPhone', 'Samsung Galaxy', 'MacBook Pro', 'Windows PC']),
            'browser_name' => $this->faker->randomElement(['Chrome', 'Firefox', 'Safari', 'Edge']),
            'browser_version' => $this->faker->randomFloat(1, 80, 120),
            'operating_system' => $this->faker->randomElement(['Windows 10', 'macOS', 'iOS', 'Android', 'Linux']),
            'platform' => $this->faker->randomElement(['Windows', 'Mac', 'iOS', 'Android', 'Linux']),
            'country' => $this->faker->country(),
            'country_code' => $this->faker->countryCode(),
            'region' => $this->faker->state(),
            'city' => $this->faker->city(),
            'timezone' => $this->faker->timezone(),
            'latitude' => $this->faker->latitude(),
            'longitude' => $this->faker->longitude(),
            'session_id' => Str::random(40),
            'session_duration' => $sessionDuration,
            'is_new_device' => $this->faker->boolean(20),
            'is_new_location' => $this->faker->boolean(15),
            'is_suspicious' => $this->faker->boolean(5),
            'security_notes' => $this->faker->boolean(5) ? $this->faker->sentence() : null,
            'additional_data' => [
                'referer' => $this->faker->url(),
                'accept_language' => 'en-US,en;q=0.9',
                'accept_encoding' => 'gzip, deflate, br',
            ],
            'is_deleted' => false,
        ];
    }

    /**
     * Indicate that the login was successful.
     */
    public function successful(): static
    {
        return $this->state(fn (array $attributes) => [
            'login_successful' => true,
            'failure_reason' => null,
        ]);
    }

    /**
     * Indicate that the login failed.
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'login_successful' => false,
            'failure_reason' => $this->faker->randomElement([
                'Invalid credentials',
                'Account locked',
                'Rate limited',
                'Account disabled'
            ]),
        ]);
    }

    /**
     * Indicate that the login is suspicious.
     */
    public function suspicious(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_suspicious' => true,
            'security_notes' => $this->faker->sentence(),
        ]);
    }

    /**
     * Indicate that this is from a new device.
     */
    public function newDevice(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_new_device' => true,
        ]);
    }

    /**
     * Indicate that this is from a new location.
     */
    public function newLocation(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_new_location' => true,
        ]);
    }

    /**
     * Indicate that the login history is deleted.
     */
    public function deleted(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_deleted' => true,
        ]);
    }
}
