@extends('layouts.admin')

@section('title', __('common.admin_dashboard') . ' - ' . __('common.company_name'))
@section('page_title', __('common.admin_dashboard'))

@push('styles')
<style>
.period-btn {
    transition: all 0.2s ease-in-out;
}
.period-btn:hover:not(.bg-blue-600) {
    background-color: rgba(229, 231, 235, 1);
}
#customDateRange {
    animation: slideDown 0.3s ease-out;
}
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
@endpush

@section('content')
<div class="p-6 space-y-6">
    <!-- Welcome Section -->
    <div class="bg-gradient-to-r from-purple-600 to-purple-700 rounded-lg p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-2xl font-bold mb-2">{{ __('common.welcome_to_admin_dashboard', ['name' => auth()->user()->first_name]) }}</h2>
                <p class="text-purple-100">{{ __('common.manage_system_monitor_performance') }}</p>
            </div>
            <div class="hidden md:block">
                <svg class="w-16 h-16 text-purple-200" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8z" clip-rule="evenodd"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- Admin Stats Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Users -->
        <a href="{{ route('admin.users.index') }}" class="dashboard-card hover:shadow-md transition-shadow">
            <div class="dashboard-stat">
                <div class="dashboard-stat-content">
                    <h3>{{ $stats['total_users'] ?? 0 }}</h3>
                    <p>{{ __('common.total_users') }}</p>
                </div>
                <div class="dashboard-stat-icon bg-blue-100 text-blue-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                </div>
            </div>
        </a>

        <!-- Total Orders -->
        <a href="{{ route('admin.orders.index') }}" class="dashboard-card hover:shadow-md transition-shadow">
            <div class="dashboard-stat">
                <div class="dashboard-stat-content">
                    <h3>{{ $stats['total_orders'] ?? 0 }}</h3>
                    <p>{{ __('common.total_orders') }}</p>
                </div>
                <div class="dashboard-stat-icon bg-green-100 text-green-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                    </svg>
                </div>
            </div>
        </a>

        <!-- Total Products -->
        <a href="{{ route('admin.products.index') }}" class="dashboard-card hover:shadow-md transition-shadow">
            <div class="dashboard-stat">
                <div class="dashboard-stat-content">
                    <h3>{{ $stats['total_products'] ?? 0 }}</h3>
                    <p>{{ __('common.total_products') }}</p>
                </div>
                <div class="dashboard-stat-icon bg-yellow-100 text-yellow-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                </div>
            </div>
        </a>

        <!-- System Status -->
        <div class="dashboard-card">
            <div class="dashboard-stat">
                <div class="dashboard-stat-content">
                    <h3>{{ __('common.online') }}</h3>
                    <p>{{ __('common.system_status') }}</p>
                </div>
                <div class="dashboard-stat-icon bg-green-100 text-green-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Visitor Analytics Section -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900">{{ __('common.visitor_analytics') }}</h3>
            <div class="flex space-x-2">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    {{ __('common.live') }}
                </span>
            </div>
        </div>

        <!-- Visitor Stats Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <!-- Today's Visitors -->
            <div class="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-blue-600">{{ __('common.todays_visitors') }}</p>
                        <p class="text-2xl font-bold text-blue-900">{{ is_array($visitor_stats['today_visitors'] ?? 0) ? 0 : ($visitor_stats['today_visitors'] ?? 0) }}</p>
                        <p class="text-xs text-blue-600">
                            {{ is_array($visitor_stats['today_new'] ?? 0) ? 0 : ($visitor_stats['today_new'] ?? 0) }} {{ __('common.new') }},
                            {{ is_array($visitor_stats['today_returning'] ?? 0) ? 0 : ($visitor_stats['today_returning'] ?? 0) }} {{ __('common.returning') }}
                        </p>
                    </div>
                    <div class="w-8 h-8 bg-blue-200 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Page Views -->
            <div class="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-green-600">{{ __('common.page_views') }}</p>
                        <p class="text-2xl font-bold text-green-900">{{ is_array($visitor_stats['today_page_views'] ?? 0) ? 0 : ($visitor_stats['today_page_views'] ?? 0) }}</p>
                        <p class="text-xs text-green-600">{{ __('common.today') }}</p>
                    </div>
                    <div class="w-8 h-8 bg-green-200 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Weekly Visitors -->
            <div class="bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-purple-600">{{ __('common.this_week') }}</p>
                        <p class="text-2xl font-bold text-purple-900">{{ is_array($visitor_stats['week_visitors'] ?? 0) ? 0 : ($visitor_stats['week_visitors'] ?? 0) }}</p>
                        <p class="text-xs text-purple-600">{{ __('common.unique_visitors') }}</p>
                    </div>
                    <div class="w-8 h-8 bg-purple-200 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Errors & Issues -->
            <div class="bg-gradient-to-r from-red-50 to-red-100 rounded-lg p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-red-600">{{ __('Issues Today') }}</p>
                        <p class="text-2xl font-bold text-red-900">{{ (is_array($visitor_stats['errors_today'] ?? 0) ? 0 : ($visitor_stats['errors_today'] ?? 0)) + (is_array($visitor_stats['suspicious_visits_today'] ?? 0) ? 0 : ($visitor_stats['suspicious_visits_today'] ?? 0)) }}</p>
                        <p class="text-xs text-red-600">
                            {{ is_array($visitor_stats['errors_today'] ?? 0) ? 0 : ($visitor_stats['errors_today'] ?? 0) }} {{ __('errors') }},
                            {{ is_array($visitor_stats['suspicious_visits_today'] ?? 0) ? 0 : ($visitor_stats['suspicious_visits_today'] ?? 0) }} {{ __('suspicious') }}
                        </p>
                    </div>
                    <div class="w-8 h-8 bg-red-200 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Visitor Chart -->
        <div class="mb-6">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
                <h4 id="chartTitle" class="text-md font-medium text-gray-900 mb-2 sm:mb-0">{{ __('Visitor Trends (Last 30 Days)') }}</h4>

                <!-- Chart Filters -->
                <div class="flex flex-wrap items-center gap-2">
                    <!-- Quick Period Buttons -->
                    <div class="flex bg-gray-100 rounded-lg p-1">
                        <button type="button" class="period-btn px-3 py-1 text-xs font-medium rounded-md transition-colors" data-period="1">1D</button>
                        <button type="button" class="period-btn px-3 py-1 text-xs font-medium rounded-md transition-colors" data-period="3">3D</button>
                        <button type="button" class="period-btn px-3 py-1 text-xs font-medium rounded-md transition-colors" data-period="5">5D</button>
                        <button type="button" class="period-btn px-3 py-1 text-xs font-medium rounded-md transition-colors" data-period="7">7D</button>
                        <button type="button" class="period-btn px-3 py-1 text-xs font-medium rounded-md transition-colors" data-period="14">14D</button>
                        <button type="button" class="period-btn px-3 py-1 text-xs font-medium rounded-md transition-colors bg-blue-600 text-white" data-period="30">30D</button>
                    </div>

                    <!-- Custom Date Range Button -->
                    <button type="button" id="customRangeBtn" class="px-3 py-1 text-xs font-medium text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                        Custom Range
                    </button>
                </div>
            </div>

            <!-- Custom Date Range Picker (Hidden by default) -->
            <div id="customDateRange" class="hidden mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div class="flex flex-col sm:flex-row sm:items-end gap-4">
                    <div class="flex-1">
                        <label for="startDate" class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                        <input type="date" id="startDate" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div class="flex-1">
                        <label for="endDate" class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                        <input type="date" id="endDate" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div class="flex gap-2">
                        <button type="button" id="applyCustomRange" class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors">
                            Apply
                        </button>
                        <button type="button" id="cancelCustomRange" class="px-4 py-2 bg-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-400 transition-colors">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>

            <div class="bg-gray-50 rounded-lg p-4 relative">
                <!-- Loading Overlay -->
                <div id="chartLoading" class="hidden absolute inset-0 bg-gray-50 bg-opacity-75 flex items-center justify-center rounded-lg z-10">
                    <div class="flex items-center space-x-2">
                        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                        <span class="text-sm text-gray-600">Loading chart data...</span>
                    </div>
                </div>
                <canvas id="visitorChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Recent Activity -->
        <div class="dashboard-card">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">{{ __('Recent Activity') }}</h3>
                <a href="{{ route('admin.activity-logs.index') }}" class="text-blue-600 hover:text-blue-700 text-sm font-medium">{{ __('View All') }}</a>
            </div>

            <div class="space-y-2 h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                @forelse($recent_activities as $activity)
                <div class="activity-item p-3 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors border border-transparent hover:border-gray-200" onclick="window.location.href='{{ $activity['url'] ?? '#' }}'">
                    @php
                        $recentActivityService = app(\App\Services\RecentActivityService::class);
                        $colorClasses = $recentActivityService->getActivityColorClasses($activity['color']);
                        $iconSvg = $recentActivityService->getActivityIcon($activity['icon']);
                    @endphp
                    <div class="activity-icon {{ $colorClasses['bg'] }} {{ $colorClasses['text'] }}">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            {!! $iconSvg !!}
                        </svg>
                    </div>
                    <div class="activity-content flex-1">
                        <h4 class="text-sm font-medium text-gray-900">{{ $activity['title'] }}</h4>
                        <p class="text-xs text-gray-500 truncate">{{ $activity['description'] }}</p>
                        <p class="text-xs text-gray-400">{{ $activity['time_ago'] }}</p>
                    </div>
                </div>
                @empty
                <div class="text-center py-4">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                    <p class="mt-2 text-sm text-gray-500">{{ __('No recent activity') }}</p>
                </div>
                @endforelse
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="dashboard-card">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">{{ __('Quick Actions') }}</h3>

            <div class="grid grid-cols-2 gap-4">
                <a href="{{ route('admin.contact-submissions.index') }}" class="quick-action relative">
                    <div class="quick-action-icon bg-orange-100 text-orange-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8l-4 4m0 0l-4-4m4 4V3"></path>
                        </svg>
                    </div>
                    @if(isset($stats['unread_contact_forms']) && $stats['unread_contact_forms'] > 0)
                        <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center">
                            {{ $stats['unread_contact_forms'] }}
                        </span>
                    @endif
                    <h3>{{ __('Contact Forms') }}</h3>
                    <p>{{ __('Manage submissions') }}</p>
                </a>

                <a href="{{ route('admin.newsletter-subscriptions.index') }}" class="quick-action relative">
                    <div class="quick-action-icon bg-indigo-100 text-indigo-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    @if(isset($stats['new_newsletter_subscriptions']) && $stats['new_newsletter_subscriptions'] > 0)
                        <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center">
                            {{ $stats['new_newsletter_subscriptions'] }}
                        </span>
                    @endif
                    <h3>{{ __('Newsletter') }}</h3>
                    <p>{{ __('Manage subscribers') }}</p>
                </a>

                <a href="{{ route('admin.job-applications.index') }}" class="quick-action relative">
                    <div class="quick-action-icon bg-pink-100 text-pink-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6M8 6V4a2 2 0 012-2h4a2 2 0 012 2v2m-8 0h8m-8 0a2 2 0 00-2 2v6a2 2 0 002 2h8a2 2 0 002-2V8a2 2 0 00-2-2z"></path>
                        </svg>
                    </div>
                    @if(isset($stats['pending_job_applications']) && $stats['pending_job_applications'] > 0)
                        <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center">
                            {{ $stats['pending_job_applications'] }}
                        </span>
                    @endif
                    <h3>{{ __('Job Applications') }}</h3>
                    <p>{{ __('Review applications') }}</p>
                </a>

                <a href="{{ route('admin.project-applications.index') }}" class="quick-action relative">
                    <div class="quick-action-icon bg-teal-100 text-teal-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    @if(isset($stats['new_project_applications']) && $stats['new_project_applications'] > 0)
                        <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center">
                            {{ $stats['new_project_applications'] }}
                        </span>
                    @endif
                    <h3>{{ __('Project Applications') }}</h3>
                    <p>{{ __('Review applications') }}</p>
                </a>
            </div>
        </div>

        <!-- Recent Visitors -->
        <div class="dashboard-card">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">{{ __('Recent Visitors') }}</h3>
                <a href="{{ route('admin.visitor-analytics.index') }}" class="text-blue-600 hover:text-blue-700 text-sm font-medium">{{ __('View All') }}</a>
            </div>

            <div class="space-y-2 h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                @forelse($recent_visitors as $visitor)
                <div class="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors border border-transparent hover:border-gray-200" onclick="showVisitorDetails('{{ $visitor['id'] ?? '' }}', '{{ $visitor['ip_address'] ?? '' }}', '{{ is_array($visitor['location'] ?? 'Unknown') ? 'Unknown' : ($visitor['location'] ?? 'Unknown') }}', '{{ is_array($visitor['device_info'] ?? 'Unknown') ? 'Unknown' : ($visitor['device_info'] ?? 'Unknown') }}', '{{ is_array($visitor['page'] ?? 'Unknown') ? 'Unknown' : ($visitor['page'] ?? 'Unknown') }}', '{{ $visitor['time_ago'] ?? '' }}', {{ $visitor['is_suspicious'] ? 'true' : 'false' }}, {{ $visitor['is_returning'] ? 'true' : 'false' }})">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 rounded-full flex items-center justify-center {{ $visitor['is_suspicious'] ? 'bg-red-100 text-red-600' : ($visitor['is_returning'] ? 'bg-green-100 text-green-600' : 'bg-blue-100 text-blue-600') }}">
                            @if($visitor['is_suspicious'])
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                            @elseif($visitor['is_returning'])
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"></path>
                                </svg>
                            @else
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                </svg>
                            @endif
                        </div>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center justify-between mb-1">
                            <p class="text-sm font-medium text-gray-900 truncate">
                                {{ is_array($visitor['location'] ?? 'Unknown Location') ? 'Unknown Location' : ($visitor['location'] ?: 'Unknown Location') }}
                            </p>
                            <p class="text-xs text-gray-500 flex-shrink-0 ml-2">{{ $visitor['time_ago'] }}</p>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex-1 min-w-0">
                                <p class="text-xs text-gray-500 truncate">{{ is_array($visitor['device_info'] ?? 'Unknown') ? 'Unknown' : ($visitor['device_info'] ?? 'Unknown') }}</p>
                                <p class="text-xs text-gray-400 truncate">{{ is_array($visitor['page'] ?? 'Unknown Page') ? 'Unknown Page' : ($visitor['page'] ?: 'Unknown Page') }}</p>
                            </div>
                            <div class="flex-shrink-0 ml-2">
                                @if($visitor['is_suspicious'])
                                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        {{ __('Suspicious') }}
                                    </span>
                                @elseif($visitor['is_returning'])
                                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        {{ __('Returning') }}
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {{ __('New') }}
                                    </span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                @empty
                <div class="text-center py-4">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                    <p class="mt-2 text-sm text-gray-500">{{ __('No recent visitors') }}</p>
                </div>
                @endforelse
            </div>
        </div>
    </div>

    <!-- Top Pages Section -->
    <div class="dashboard-card">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">{{ __('Top Pages') }}</h3>
            <div class="flex items-center space-x-2">
                <select id="topPagesFilter" onchange="updateTopPages()" class="text-xs border-gray-300 rounded-md focus:border-blue-500 focus:ring-blue-500">
                    <option value="1">{{ __('Last 1 Day') }}</option>
                    <option value="3">{{ __('Last 3 Days') }}</option>
                    <option value="7" selected>{{ __('Last 7 Days') }}</option>
                    <option value="custom">{{ __('Custom Range') }}</option>
                </select>
                <div id="customDateRange" class="hidden flex items-center space-x-1">
                    <input type="date" id="startDate" class="text-xs border-gray-300 rounded-md focus:border-blue-500 focus:ring-blue-500">
                    <span class="text-xs text-gray-500">to</span>
                    <input type="date" id="endDate" class="text-xs border-gray-300 rounded-md focus:border-blue-500 focus:ring-blue-500">
                    <button onclick="updateTopPages()" class="text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600">
                        {{ __('common.apply') }}
                    </button>
                </div>
            </div>
        </div>

        <div id="topPagesContent" class="space-y-3 h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
            @forelse($top_pages as $page)
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                <div class="flex-1">
                    <h4 class="text-sm font-medium text-gray-900">{{ (is_array($page) ? ($page['page_title'] ?? '') : ($page->page_title ?? '')) ?: (is_array($page) ? ($page['route_name'] ?? '') : ($page->route_name ?? '')) }}</h4>
                    <p class="text-xs text-gray-500">{{ is_array($page) ? ($page['route_name'] ?? '') : ($page->route_name ?? '') }}</p>
                </div>
                <div class="text-right">
                    <p class="text-sm font-semibold text-gray-900">{{ number_format(is_array($page) ? ($page['visits'] ?? 0) : ($page->visits ?? 0)) }}</p>
                    <p class="text-xs text-gray-500">{{ number_format(is_array($page) ? ($page['unique_visitors'] ?? 0) : ($page->unique_visitors ?? 0)) }} {{ __('unique') }}</p>
                </div>
            </div>
            @empty
            <div class="text-center py-4">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                <p class="mt-2 text-sm text-gray-500">{{ __('No page data available') }}</p>
            </div>
            @endforelse
        </div>
    </div>
</div>

<!-- Visitor Details Modal -->
<div id="visitorModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">{{ __('Visitor Details') }}</h3>
                <button onclick="closeVisitorModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <div id="visitorDetailsContent" class="space-y-4">
                <!-- Content will be populated by JavaScript -->
            </div>

            <div class="flex justify-end mt-6 space-x-3">
                <button onclick="closeVisitorModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                    {{ __('Close') }}
                </button>
                <a id="viewFullAnalytics" href="#" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
                    {{ __('View Full Analytics') }}
                </a>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
/* Custom scrollbar styles */
.scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: #d1d5db #f3f4f6;
}

.scrollbar-thin::-webkit-scrollbar {
    width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
    background: #f3f4f6;
    border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('visitorChart').getContext('2d');
    const chartData = @json($visitor_chart_data);
    let visitorChart;

    // Initialize chart
    function initChart(data) {
        if (visitorChart) {
            visitorChart.destroy();
        }

        visitorChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.map(item => {
                    const date = new Date(item.date);
                    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                }),
                datasets: [{
                    label: '{{ __("Unique Visitors") }}',
                    data: data.map(item => item.unique_visitors),
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.1,
                    fill: true
                }, {
                    label: '{{ __("Page Views") }}',
                    data: data.map(item => item.page_views),
                    borderColor: 'rgb(16, 185, 129)',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    tension: 0.1,
                    fill: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                    }
                },
                interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false
                }
            }
        });
    }

    // Initialize chart with default data
    initChart(chartData);

    // Show/hide loading overlay
    function showLoading() {
        document.getElementById('chartLoading').classList.remove('hidden');
    }

    function hideLoading() {
        document.getElementById('chartLoading').classList.add('hidden');
    }

    // Update chart title
    function updateChartTitle(title) {
        document.getElementById('chartTitle').textContent = title;
    }

    // Update period button states
    function updatePeriodButtons(activePeriod) {
        document.querySelectorAll('.period-btn').forEach(btn => {
            btn.classList.remove('bg-blue-600', 'text-white');
            btn.classList.add('text-gray-600', 'hover:bg-gray-200');
        });

        if (activePeriod !== 'custom') {
            const activeBtn = document.querySelector(`[data-period="${activePeriod}"]`);
            if (activeBtn) {
                activeBtn.classList.remove('text-gray-600', 'hover:bg-gray-200');
                activeBtn.classList.add('bg-blue-600', 'text-white');
            }
        }
    }

    // Fetch chart data via AJAX
    function fetchChartData(period, startDate = null, endDate = null) {
        showLoading();

        const params = new URLSearchParams({ period });
        if (period === 'custom' && startDate && endDate) {
            params.append('start_date', startDate);
            params.append('end_date', endDate);
        }

        fetch(`{{ route('admin.dashboard.visitor-chart-data') }}?${params}`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                initChart(data.data);
                updateChartTitle(data.title);
                updatePeriodButtons(data.period);
            } else {
                console.error('Failed to fetch chart data:', data);
                alert('Failed to load chart data. Please try again.');
            }
        })
        .catch(error => {
            console.error('Error fetching chart data:', error);
            alert('An error occurred while loading chart data. Please try again.');
        })
        .finally(() => {
            hideLoading();
        });
    }

    // Period button event listeners
    document.querySelectorAll('.period-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const period = this.dataset.period;
            fetchChartData(period);

            // Hide custom date range if visible
            document.getElementById('customDateRange').classList.add('hidden');
        });
    });

    // Custom range button event listener
    document.getElementById('customRangeBtn').addEventListener('click', function() {
        const customRange = document.getElementById('customDateRange');
        customRange.classList.toggle('hidden');

        if (!customRange.classList.contains('hidden')) {
            // Set default dates (last 30 days to today)
            const today = new Date();
            const thirtyDaysAgo = new Date(today);
            thirtyDaysAgo.setDate(today.getDate() - 30);

            document.getElementById('endDate').value = today.toISOString().split('T')[0];
            document.getElementById('startDate').value = thirtyDaysAgo.toISOString().split('T')[0];
        }
    });

    // Apply custom range
    document.getElementById('applyCustomRange').addEventListener('click', function() {
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;

        if (!startDate || !endDate) {
            alert('Please select both start and end dates.');
            return;
        }

        if (new Date(startDate) > new Date(endDate)) {
            alert('Start date must be before or equal to end date.');
            return;
        }

        fetchChartData('custom', startDate, endDate);
        document.getElementById('customDateRange').classList.add('hidden');
    });

    // Cancel custom range
    document.getElementById('cancelCustomRange').addEventListener('click', function() {
        document.getElementById('customDateRange').classList.add('hidden');
    });

    // Top Pages Filter Functionality
    document.getElementById('topPagesFilter').addEventListener('change', function() {
        const customRange = document.getElementById('customDateRange');
        if (this.value === 'custom') {
            customRange.classList.remove('hidden');
            // Set default dates
            const today = new Date();
            const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            document.getElementById('endDate').value = today.toISOString().split('T')[0];
            document.getElementById('startDate').value = weekAgo.toISOString().split('T')[0];
        } else {
            customRange.classList.add('hidden');
            updateTopPages();
        }
    });
});

// Visitor Details Modal Functions
function showVisitorDetails(id, ip, location, device, page, timeAgo, isSuspicious, isReturning) {
    const modal = document.getElementById('visitorModal');
    const content = document.getElementById('visitorDetailsContent');
    const fullAnalyticsLink = document.getElementById('viewFullAnalytics');

    // Populate modal content
    content.innerHTML = `
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-3">
                <div>
                    <label class="block text-sm font-medium text-gray-700">{{ __('IP Address') }}</label>
                    <p class="text-sm text-gray-900">${ip || 'Unknown'}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">{{ __('Location') }}</label>
                    <p class="text-sm text-gray-900">${location}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">{{ __('Device Info') }}</label>
                    <p class="text-sm text-gray-900">${device}</p>
                </div>
            </div>
            <div class="space-y-3">
                <div>
                    <label class="block text-sm font-medium text-gray-700">{{ __('Last Page') }}</label>
                    <p class="text-sm text-gray-900">${page}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">{{ __('Visit Time') }}</label>
                    <p class="text-sm text-gray-900">${timeAgo}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">{{ __('Status') }}</label>
                    <div class="flex space-x-2">
                        ${isSuspicious ? '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">{{ __("Suspicious") }}</span>' : ''}
                        ${isReturning ? '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">{{ __("Returning") }}</span>' : '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">{{ __("New") }}</span>'}
                    </div>
                </div>
            </div>
        </div>
    `;

    // Set full analytics link
    fullAnalyticsLink.href = `{{ route('admin.visitor-analytics.index') }}?search=${ip}`;

    // Show modal
    modal.classList.remove('hidden');
}

function closeVisitorModal() {
    document.getElementById('visitorModal').classList.add('hidden');
}

// Top Pages Update Function
function updateTopPages() {
    const filter = document.getElementById('topPagesFilter').value;
    const content = document.getElementById('topPagesContent');

    let url = '{{ route("admin.dashboard.top-pages") }}';
    let params = new URLSearchParams();

    if (filter === 'custom') {
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        if (startDate && endDate) {
            params.append('start_date', startDate);
            params.append('end_date', endDate);
        }
    } else {
        params.append('days', filter);
    }

    // Show loading state
    content.innerHTML = '<div class="text-center py-4"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div></div>';

    fetch(`${url}?${params.toString()}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                content.innerHTML = data.html;
            } else {
                content.innerHTML = '<div class="text-center py-4 text-red-500">{{ __("Error loading data") }}</div>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            content.innerHTML = '<div class="text-center py-4 text-red-500">{{ __("Error loading data") }}</div>';
        });
}

// Close modal when clicking outside
document.getElementById('visitorModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeVisitorModal();
    }
});
</script>

<style>
/* Custom scrollbar styling */
.scrollbar-thin::-webkit-scrollbar {
    width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Ensure proper containment for dashboard cards */
.dashboard-card {
    overflow: hidden; /* Prevent content from escaping the card */
}

/* Activity item styling */
.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    min-height: 60px;
}

.activity-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
    min-width: 0;
}

.activity-content h4 {
    margin: 0 0 4px 0;
    font-weight: 500;
}

.activity-content p {
    margin: 0;
    line-height: 1.4;
}

/* Visitor item styling */
.dashboard-card .space-y-2 > div {
    min-height: 60px;
}
</style>
@endpush

@endsection
