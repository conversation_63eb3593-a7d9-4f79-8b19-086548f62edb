<?php

namespace App\Http\Controllers;

use App\Models\LoginHistory;
use App\Models\User;
use App\Services\LoginHistoryService;
use App\Services\ActivityLogger;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class LoginHistoryController extends Controller
{
    protected LoginHistoryService $loginHistoryService;
    protected ActivityLogger $activityLogger;

    public function __construct(LoginHistoryService $loginHistoryService, ActivityLogger $activityLogger)
    {
        $this->loginHistoryService = $loginHistoryService;
        $this->activityLogger = $activityLogger;
    }

    /**
     * Display login history for the authenticated user.
     */
    public function index(Request $request): View
    {
        $user = Auth::user();
        $perPage = $request->get('per_page', 15);

        // Handle date filtering
        $query = $user->loginHistories()->where('is_deleted', false);

        if ($request->has('start_date') && $request->has('end_date')) {
            $startDate = $request->get('start_date');
            $endDate = $request->get('end_date');
            $query->whereBetween('login_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59']);
            $statsDays = now()->diffInDays($startDate);
        } elseif ($request->has('days') && $request->get('days') !== 'all') {
            $days = (int) $request->get('days');
            $query->where('login_at', '>=', now()->subDays($days));
            $statsDays = $days;
        } else {
            // Default to last 30 days
            $query->where('login_at', '>=', now()->subDays(30));
            $statsDays = 30;
        }

        $loginHistories = $query->paginate($perPage);
        $stats = $this->loginHistoryService->getLoginStats($user, $statsDays ?? 30);

        // Log activity
        $this->activityLogger->logActivity(
            'login_history_view',
            'User viewed their own login history',
            'success',
            null,
            [
                'per_page' => $perPage,
                'total_records' => $loginHistories->total(),
                'date_filter' => $request->only(['days', 'start_date', 'end_date'])
            ],
            [],
            10
        );

        return view('profile.login-history', compact('loginHistories', 'stats'));
    }

    /**
     * Display login history for a specific user (admin only).
     */
    public function show(Request $request, User $user): View
    {
        // Check if user has permission to view other users' login history
        if (!Auth::user()->isAdmin() && Auth::id() !== $user->id) {
            // Log unauthorized access attempt
            $this->activityLogger->logSecurityEvent(
                'unauthorized_login_history_access',
                [
                    'target_user_id' => $user->id,
                    'target_user_email' => $user->email,
                    'attempted_by' => Auth::user()->email,
                ],
                'high'
            );
            abort(403, 'Unauthorized to view this user\'s login history.');
        }

        $perPage = $request->get('per_page', 15);

        // Handle date filtering
        $query = $user->loginHistories()->where('is_deleted', false);

        if ($request->has('start_date') && $request->has('end_date')) {
            $startDate = $request->get('start_date');
            $endDate = $request->get('end_date');
            $query->whereBetween('login_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59']);
            $days = now()->diffInDays($startDate);
        } elseif ($request->has('days') && $request->get('days') !== 'all') {
            $days = (int) $request->get('days');
            $query->where('login_at', '>=', now()->subDays($days));
        } else {
            // Default to last 30 days if no filter specified
            $days = $request->get('days', 30);
            if ($days !== 'all') {
                $query->where('login_at', '>=', now()->subDays($days));
            }
        }

        $loginHistories = $query->paginate($perPage);
        $stats = $this->loginHistoryService->getLoginStats($user, is_numeric($days) ? $days : 365);

        // Log admin access to user login history
        $this->activityLogger->logActivity(
            'admin_login_history_view',
            "Admin viewed login history for user: {$user->email}",
            'success',
            null,
            [
                'target_user_id' => $user->id,
                'target_user_email' => $user->email,
                'days_filter' => $days,
                'per_page' => $perPage,
                'total_records' => $loginHistories->total()
            ],
            [],
            15
        );

        return view('admin.users.login-history', compact('user', 'loginHistories', 'stats', 'days'));
    }

    /**
     * Get login history data as JSON (for AJAX requests).
     */
    public function data(Request $request): JsonResponse
    {
        $user = Auth::user();
        $perPage = $request->get('per_page', 15);
        $days = $request->get('days', 30);

        $loginHistories = $user->loginHistories()
            ->where('is_deleted', false)
            ->where('login_at', '>=', now()->subDays($days))
            ->paginate($perPage);

        // Filter sensitive data for non-admin users
        if (!$user->isAdmin()) {
            $loginHistories->getCollection()->transform(function ($history) {
                return [
                    'id' => $history->id,
                    'login_at' => $history->login_at,
                    'logout_at' => $history->logout_at,
                    'login_successful' => $history->login_successful,
                    'ip_address' => $history->ip_address,
                    'device_type' => $history->device_type,
                    'browser_name' => $history->browser_name,
                    'operating_system' => $history->operating_system,
                    'location' => $history->location,
                    'session_duration_human' => $history->session_duration_human,
                    'is_new_device' => $history->is_new_device,
                    'is_new_location' => $history->is_new_location,
                ];
            });
        }

        return response()->json($loginHistories);
    }

    /**
     * Mark a login history entry as suspicious (admin only).
     */
    public function markSuspicious(Request $request, User $user, LoginHistory $loginHistory): JsonResponse
    {
        if (!Auth::user()->isAdmin()) {
            // Log unauthorized access attempt
            $this->activityLogger->logSecurityEvent(
                'unauthorized_mark_suspicious_attempt',
                [
                    'login_history_id' => $loginHistory->id,
                    'target_user_id' => $loginHistory->user_id,
                    'attempted_by' => Auth::user()->email,
                ],
                'high'
            );
            abort(403, 'Unauthorized action.');
        }

        $request->validate([
            'reason' => 'nullable|string|max:500'
        ]);

        $reason = $request->input('reason');
        $loginHistory->markAsSuspicious($reason);

        // Log the action
        $this->activityLogger->logActivity(
            'login_history_mark_suspicious',
            "Admin marked login history as suspicious for user: {$loginHistory->user->email}",
            'success',
            null,
            [
                'login_history_id' => $loginHistory->id,
                'target_user_id' => $loginHistory->user_id,
                'target_user_email' => $loginHistory->user->email,
                'reason' => $reason,
                'login_date' => $loginHistory->login_at->toISOString(),
                'ip_address' => $loginHistory->ip_address
            ],
            [],
            20
        );

        return response()->json([
            'success' => true,
            'message' => 'Login history marked as suspicious.'
        ]);
    }

    /**
     * Delete a login history entry (soft delete).
     */
    public function destroy(LoginHistory $loginHistory): JsonResponse
    {
        // Users can only delete their own login history, admins can delete any
        if (!Auth::user()->isAdmin() && Auth::id() !== $loginHistory->user_id) {
            // Log unauthorized access attempt
            $this->activityLogger->logSecurityEvent(
                'unauthorized_login_history_delete_attempt',
                [
                    'login_history_id' => $loginHistory->id,
                    'target_user_id' => $loginHistory->user_id,
                    'attempted_by' => Auth::user()->email,
                ],
                'high'
            );
            abort(403, 'Unauthorized action.');
        }

        $loginHistory->update(['is_deleted' => true]);

        // Log the deletion
        $this->activityLogger->logActivity(
            'login_history_delete',
            Auth::user()->isAdmin()
                ? "Admin deleted login history entry for user: {$loginHistory->user->email}"
                : "User deleted their own login history entry",
            'success',
            null,
            [
                'login_history_id' => $loginHistory->id,
                'target_user_id' => $loginHistory->user_id,
                'target_user_email' => $loginHistory->user->email,
                'login_date' => $loginHistory->login_at->toISOString(),
                'ip_address' => $loginHistory->ip_address,
                'deleted_by_admin' => Auth::user()->isAdmin()
            ],
            [],
            15
        );

        return response()->json([
            'success' => true,
            'message' => 'Login history entry deleted.'
        ]);
    }

    /**
     * Get login statistics for a user.
     */
    public function stats(Request $request, User $user = null): JsonResponse
    {
        $targetUser = $user ?? Auth::user();

        // Check permissions
        if (!Auth::user()->isAdmin() && Auth::id() !== $targetUser->id) {
            abort(403, 'Unauthorized to view this user\'s statistics.');
        }

        $days = $request->get('days', 30);
        $stats = $this->loginHistoryService->getLoginStats($targetUser, $days);

        return response()->json($stats);
    }

    /**
     * Export login history as CSV (admin only).
     */
    public function export(Request $request, User $user): \Symfony\Component\HttpFoundation\StreamedResponse
    {
        if (!Auth::user()->isAdmin()) {
            abort(403, 'Unauthorized action.');
        }

        $days = $request->get('days', 30);

        $loginHistories = $user->loginHistories()
            ->where('is_deleted', false)
            ->where('login_at', '>=', now()->subDays($days))
            ->orderBy('login_at', 'desc')
            ->get();

        $filename = "login_history_{$user->uuid}_{now()->format('Y-m-d')}.csv";

        return response()->streamDownload(function () use ($loginHistories) {
            $handle = fopen('php://output', 'w');

            // CSV headers
            fputcsv($handle, [
                'Login Date',
                'Logout Date',
                'Success',
                'Method',
                'IP Address',
                'Device Type',
                'Browser',
                'OS',
                'Location',
                'Session Duration',
                'New Device',
                'New Location',
                'Suspicious'
            ]);

            // CSV data
            foreach ($loginHistories as $history) {
                fputcsv($handle, [
                    $history->login_at->format('Y-m-d H:i:s'),
                    $history->logout_at?->format('Y-m-d H:i:s') ?? 'N/A',
                    $history->login_successful ? 'Yes' : 'No',
                    $history->login_method,
                    $history->ip_address,
                    $history->device_type,
                    $history->browser_name . ' ' . $history->browser_version,
                    $history->operating_system,
                    $history->location,
                    $history->session_duration_human,
                    $history->is_new_device ? 'Yes' : 'No',
                    $history->is_new_location ? 'Yes' : 'No',
                    $history->is_suspicious ? 'Yes' : 'No',
                ]);
            }

            fclose($handle);
        }, $filename, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ]);
    }
}
