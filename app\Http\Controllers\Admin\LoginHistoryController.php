<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\LoginHistory;
use App\Models\User;
use App\Services\LoginHistoryService;
use App\Services\ActivityLogger;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class LoginHistoryController extends Controller
{
    protected LoginHistoryService $loginHistoryService;
    protected ActivityLogger $activityLogger;

    public function __construct(LoginHistoryService $loginHistoryService, ActivityLogger $activityLogger)
    {
        $this->loginHistoryService = $loginHistoryService;
        $this->activityLogger = $activityLogger;
    }

    /**
     * Display global login history dashboard for all users.
     */
    public function index(Request $request): View
    {
        $perPage = $request->get('per_page', 25);
        $search = $request->get('search');
        $userFilter = $request->get('user_id');
        $statusFilter = $request->get('status'); // successful, failed, suspicious
        
        // Build query
        $query = LoginHistory::with('user')
            ->where('is_deleted', false);
        
        // Handle date filtering
        if ($request->has('start_date') && $request->has('end_date')) {
            $startDate = $request->get('start_date');
            $endDate = $request->get('end_date');
            $query->whereBetween('login_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59']);
        } elseif ($request->has('days') && $request->get('days') !== 'all') {
            $days = (int) $request->get('days');
            $query->where('login_at', '>=', now()->subDays($days));
        } else {
            // Default to last 30 days
            $query->where('login_at', '>=', now()->subDays(30));
        }
        
        // Search filter
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->whereHas('user', function ($userQuery) use ($search) {
                    $userQuery->where('email', 'like', "%{$search}%")
                             ->orWhere('first_name', 'like', "%{$search}%")
                             ->orWhere('last_name', 'like', "%{$search}%");
                })
                ->orWhere('ip_address', 'like', "%{$search}%")
                ->orWhere('device_type', 'like', "%{$search}%")
                ->orWhere('browser_name', 'like', "%{$search}%")
                ->orWhere('country', 'like', "%{$search}%")
                ->orWhere('city', 'like', "%{$search}%");
            });
        }
        
        // User filter
        if ($userFilter) {
            $query->where('user_id', $userFilter);
        }
        
        // Status filter
        if ($statusFilter) {
            switch ($statusFilter) {
                case 'successful':
                    $query->where('login_successful', true);
                    break;
                case 'failed':
                    $query->where('login_successful', false);
                    break;
                case 'suspicious':
                    $query->where('is_suspicious', true);
                    break;
                case 'new_device':
                    $query->where('is_new_device', true);
                    break;
                case 'new_location':
                    $query->where('is_new_location', true);
                    break;
            }
        }
        
        $loginHistories = $query->orderBy('login_at', 'desc')->paginate($perPage);
        
        // Get statistics
        $stats = $this->getGlobalStats($request);
        
        // Get users for filter dropdown
        $users = User::select('id', 'first_name', 'last_name', 'email')
            ->where('is_active', true)
            ->where('is_deleted', false)
            ->orderBy('first_name')
            ->get();
        
        // Log admin access
        $this->activityLogger->logActivity(
            'admin_global_login_history_view',
            'Admin viewed global login history dashboard',
            'success',
            null,
            [
                'total_records' => $loginHistories->total(),
                'filters' => $request->only(['search', 'user_id', 'status', 'days', 'start_date', 'end_date']),
                'per_page' => $perPage
            ],
            [],
            15
        );
        
        return view('admin.login-history.index', compact(
            'loginHistories', 
            'stats', 
            'users',
            'search',
            'userFilter',
            'statusFilter'
        ));
    }

    /**
     * Get login history data as JSON for AJAX requests.
     */
    public function data(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 25);
        $search = $request->get('search');
        $userFilter = $request->get('user_id');
        $statusFilter = $request->get('status');
        
        // Build query (same logic as index method)
        $query = LoginHistory::with('user')
            ->where('is_deleted', false);
        
        // Apply filters (same as index method)
        if ($request->has('start_date') && $request->has('end_date')) {
            $startDate = $request->get('start_date');
            $endDate = $request->get('end_date');
            $query->whereBetween('login_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59']);
        } elseif ($request->has('days') && $request->get('days') !== 'all') {
            $days = (int) $request->get('days');
            $query->where('login_at', '>=', now()->subDays($days));
        }
        
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->whereHas('user', function ($userQuery) use ($search) {
                    $userQuery->where('email', 'like', "%{$search}%")
                             ->orWhere('first_name', 'like', "%{$search}%")
                             ->orWhere('last_name', 'like', "%{$search}%");
                })
                ->orWhere('ip_address', 'like', "%{$search}%")
                ->orWhere('device_type', 'like', "%{$search}%")
                ->orWhere('browser_name', 'like', "%{$search}%")
                ->orWhere('country', 'like', "%{$search}%")
                ->orWhere('city', 'like', "%{$search}%");
            });
        }
        
        if ($userFilter) {
            $query->where('user_id', $userFilter);
        }
        
        if ($statusFilter) {
            switch ($statusFilter) {
                case 'successful':
                    $query->where('login_successful', true);
                    break;
                case 'failed':
                    $query->where('login_successful', false);
                    break;
                case 'suspicious':
                    $query->where('is_suspicious', true);
                    break;
                case 'new_device':
                    $query->where('is_new_device', true);
                    break;
                case 'new_location':
                    $query->where('is_new_location', true);
                    break;
            }
        }
        
        $loginHistories = $query->orderBy('login_at', 'desc')->paginate($perPage);
        
        return response()->json($loginHistories);
    }

    /**
     * Get global login statistics.
     */
    public function stats(Request $request): JsonResponse
    {
        $stats = $this->getGlobalStats($request);
        return response()->json($stats);
    }

    /**
     * Export global login history as CSV.
     */
    public function export(Request $request): \Symfony\Component\HttpFoundation\StreamedResponse
    {
        $search = $request->get('search');
        $userFilter = $request->get('user_id');
        $statusFilter = $request->get('status');
        
        // Build query for export
        $query = LoginHistory::with('user')
            ->where('is_deleted', false);
        
        // Apply same filters as index
        if ($request->has('start_date') && $request->has('end_date')) {
            $startDate = $request->get('start_date');
            $endDate = $request->get('end_date');
            $query->whereBetween('login_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59']);
        } elseif ($request->has('days') && $request->get('days') !== 'all') {
            $days = (int) $request->get('days');
            $query->where('login_at', '>=', now()->subDays($days));
        }
        
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->whereHas('user', function ($userQuery) use ($search) {
                    $userQuery->where('email', 'like', "%{$search}%")
                             ->orWhere('first_name', 'like', "%{$search}%")
                             ->orWhere('last_name', 'like', "%{$search}%");
                })
                ->orWhere('ip_address', 'like', "%{$search}%");
            });
        }
        
        if ($userFilter) {
            $query->where('user_id', $userFilter);
        }
        
        if ($statusFilter) {
            switch ($statusFilter) {
                case 'successful':
                    $query->where('login_successful', true);
                    break;
                case 'failed':
                    $query->where('login_successful', false);
                    break;
                case 'suspicious':
                    $query->where('is_suspicious', true);
                    break;
            }
        }
        
        $loginHistories = $query->orderBy('login_at', 'desc')->get();
        
        $filename = "global_login_history_" . now()->format('Y-m-d_H-i-s') . ".csv";
        
        // Log export activity
        $this->activityLogger->logActivity(
            'admin_global_login_history_export',
            'Admin exported global login history data',
            'success',
            null,
            [
                'total_records' => $loginHistories->count(),
                'filters' => $request->only(['search', 'user_id', 'status', 'days', 'start_date', 'end_date']),
                'filename' => $filename
            ],
            [],
            20
        );
        
        return response()->streamDownload(function () use ($loginHistories) {
            $handle = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($handle, [
                'User ID',
                'User Name',
                'User Email',
                'Login Date',
                'Logout Date',
                'Success',
                'Method',
                'IP Address',
                'Device Type',
                'Browser',
                'OS',
                'Country',
                'City',
                'Session Duration',
                'New Device',
                'New Location',
                'Suspicious',
                'Security Notes'
            ]);
            
            // CSV data
            foreach ($loginHistories as $history) {
                fputcsv($handle, [
                    $history->user_id,
                    $history->user ? $history->user->full_name : 'Unknown',
                    $history->user ? $history->user->email : 'Unknown',
                    $history->login_at->format('Y-m-d H:i:s'),
                    $history->logout_at?->format('Y-m-d H:i:s') ?? 'N/A',
                    $history->login_successful ? 'Yes' : 'No',
                    $history->login_method,
                    $history->ip_address,
                    $history->device_type,
                    $history->browser_name . ' ' . $history->browser_version,
                    $history->operating_system,
                    $history->country,
                    $history->city,
                    $history->session_duration_human,
                    $history->is_new_device ? 'Yes' : 'No',
                    $history->is_new_location ? 'Yes' : 'No',
                    $history->is_suspicious ? 'Yes' : 'No',
                    $history->security_notes ?? ''
                ]);
            }
            
            fclose($handle);
        }, $filename, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ]);
    }

    /**
     * Calculate global statistics.
     */
    protected function getGlobalStats(Request $request): array
    {
        $query = LoginHistory::where('is_deleted', false);
        
        // Apply date filter to stats
        if ($request->has('start_date') && $request->has('end_date')) {
            $startDate = $request->get('start_date');
            $endDate = $request->get('end_date');
            $query->whereBetween('login_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59']);
        } elseif ($request->has('days') && $request->get('days') !== 'all') {
            $days = (int) $request->get('days');
            $query->where('login_at', '>=', now()->subDays($days));
        } else {
            $query->where('login_at', '>=', now()->subDays(30));
        }
        
        $totalLogins = $query->count();
        $successfulLogins = $query->where('login_successful', true)->count();
        $failedLogins = $query->where('login_successful', false)->count();
        $suspiciousLogins = $query->where('is_suspicious', true)->count();
        $newDeviceLogins = $query->where('is_new_device', true)->count();
        $newLocationLogins = $query->where('is_new_location', true)->count();
        $uniqueUsers = $query->distinct('user_id')->count();
        $uniqueIPs = $query->distinct('ip_address')->count();
        
        return [
            'total_logins' => $totalLogins,
            'successful_logins' => $successfulLogins,
            'failed_logins' => $failedLogins,
            'suspicious_logins' => $suspiciousLogins,
            'new_device_logins' => $newDeviceLogins,
            'new_location_logins' => $newLocationLogins,
            'unique_users' => $uniqueUsers,
            'unique_ips' => $uniqueIPs,
            'success_rate' => $totalLogins > 0 ? round(($successfulLogins / $totalLogins) * 100, 2) : 0,
            'suspicious_rate' => $totalLogins > 0 ? round(($suspiciousLogins / $totalLogins) * 100, 2) : 0,
        ];
    }

    /**
     * Mark a login history entry as suspicious (admin only).
     */
    public function markSuspicious(Request $request, LoginHistory $loginHistory): JsonResponse
    {
        $request->validate([
            'reason' => 'nullable|string|max:500'
        ]);

        $reason = $request->input('reason');
        $loginHistory->markAsSuspicious($reason);

        // Log the action
        $this->activityLogger->logActivity(
            'admin_global_login_history_mark_suspicious',
            "Admin marked login history as suspicious for user: {$loginHistory->user->email}",
            'success',
            null,
            [
                'login_history_id' => $loginHistory->id,
                'target_user_id' => $loginHistory->user_id,
                'target_user_email' => $loginHistory->user->email,
                'reason' => $reason,
                'login_date' => $loginHistory->login_at->toISOString(),
                'ip_address' => $loginHistory->ip_address
            ],
            [],
            20
        );

        return response()->json([
            'success' => true,
            'message' => 'Login history marked as suspicious.'
        ]);
    }

    /**
     * Delete a login history entry (soft delete).
     */
    public function destroy(LoginHistory $loginHistory): JsonResponse
    {
        $loginHistory->update(['is_deleted' => true]);

        // Log the deletion
        $this->activityLogger->logActivity(
            'admin_global_login_history_delete',
            "Admin deleted login history entry for user: {$loginHistory->user->email}",
            'success',
            null,
            [
                'login_history_id' => $loginHistory->id,
                'target_user_id' => $loginHistory->user_id,
                'target_user_email' => $loginHistory->user->email,
                'login_date' => $loginHistory->login_at->toISOString(),
                'ip_address' => $loginHistory->ip_address
            ],
            [],
            15
        );

        return response()->json([
            'success' => true,
            'message' => 'Login history entry deleted.'
        ]);
    }
}
